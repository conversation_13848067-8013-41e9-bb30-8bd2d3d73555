<?php
/**
 * Test dosyası - Tutor LMS kurs yayınlama izni testi
 * Bu dosyayı WordPress admin panelinde çalıştırarak ayarları kontrol edebilirsiniz
 */

// Doğrudan erişimi engelle
if (!defined('ABSPATH')) {
    exit;
}

/**
 * Tutor LMS kurs yayınlama ayarlarını test et
 */
function test_tutor_course_publishing_settings() {
    echo '<div style="background: #fff; padding: 20px; margin: 20px; border: 1px solid #ccc;">';
    echo '<h2>Tutor LMS Kurs Yayınlama Ayarları Test Sonuçları</h2>';
    
    // Tutor LMS aktif mi kontrol et
    if (!class_exists('TUTOR\Tutor')) {
        echo '<p style="color: red;"><strong>HATA:</strong> Tutor LMS eklentisi aktif değil!</p>';
        echo '</div>';
        return;
    }
    
    echo '<p style="color: green;"><strong>✓</strong> Tutor LMS eklentisi aktif</p>';
    
    // Tutor ayarlarını kontrol et
    $tutor_options = get_option('tutor_option', []);
    $instructor_can_publish = isset($tutor_options['instructor_can_publish_course']) ? $tutor_options['instructor_can_publish_course'] : 'off';
    
    echo '<h3>Tutor LMS Ayarları:</h3>';
    echo '<p><strong>instructor_can_publish_course:</strong> ' . $instructor_can_publish;
    
    if ($instructor_can_publish === 'on') {
        echo ' <span style="color: green;">✓ ETKİN</span></p>';
    } else {
        echo ' <span style="color: red;">✗ PASİF</span></p>';
    }
    
    // Tutor Instructor rolü kontrol et
    $instructor_role = get_role('tutor_instructor');
    if ($instructor_role) {
        echo '<p style="color: green;"><strong>✓</strong> tutor_instructor rolü mevcut</p>';
        
        // publish_tutor_courses yetkisi kontrol et
        if ($instructor_role->has_cap('publish_tutor_courses')) {
            echo '<p style="color: green;"><strong>✓</strong> publish_tutor_courses yetkisi mevcut</p>';
        } else {
            echo '<p style="color: red;"><strong>✗</strong> publish_tutor_courses yetkisi eksik</p>';
        }
        
        // Diğer önemli yetkiler
        $important_caps = [
            'edit_tutor_courses',
            'edit_tutor_course',
            'read_tutor_course',
            'delete_tutor_course'
        ];
        
        echo '<h4>Diğer Kurs Yetkileri:</h4>';
        foreach ($important_caps as $cap) {
            if ($instructor_role->has_cap($cap)) {
                echo '<p style="color: green;"><strong>✓</strong> ' . $cap . '</p>';
            } else {
                echo '<p style="color: red;"><strong>✗</strong> ' . $cap . '</p>';
            }
        }
    } else {
        echo '<p style="color: red;"><strong>✗</strong> tutor_instructor rolü bulunamadı</p>';
    }
    
    // Mevcut kullanıcı kontrol et
    $current_user = wp_get_current_user();
    echo '<h3>Mevcut Kullanıcı:</h3>';
    echo '<p><strong>Kullanıcı ID:</strong> ' . $current_user->ID . '</p>';
    echo '<p><strong>Kullanıcı Adı:</strong> ' . $current_user->user_login . '</p>';
    echo '<p><strong>Roller:</strong> ' . implode(', ', $current_user->roles) . '</p>';
    
    if (in_array('tutor_instructor', $current_user->roles)) {
        echo '<p style="color: green;"><strong>✓</strong> Mevcut kullanıcı tutor_instructor rolünde</p>';
        
        if (current_user_can('publish_tutor_courses')) {
            echo '<p style="color: green;"><strong>✓</strong> Mevcut kullanıcı kurs yayınlayabilir</p>';
        } else {
            echo '<p style="color: red;"><strong>✗</strong> Mevcut kullanıcı kurs yayınlayamaz</p>';
        }
    } else {
        echo '<p style="color: orange;"><strong>!</strong> Mevcut kullanıcı tutor_instructor rolünde değil</p>';
    }
    
    // Role Custom eklentisi kontrol et
    if (class_exists('Role_Custom')) {
        echo '<p style="color: green;"><strong>✓</strong> Role Custom eklentisi aktif</p>';
    } else {
        echo '<p style="color: red;"><strong>✗</strong> Role Custom eklentisi aktif değil</p>';
    }
    
    echo '<hr>';
    echo '<h3>Test Sonucu:</h3>';
    
    if ($instructor_can_publish === 'on' && $instructor_role && $instructor_role->has_cap('publish_tutor_courses')) {
        echo '<p style="color: green; font-size: 16px; font-weight: bold;">✓ BAŞARILI: Tutor Instructor rolündeki kullanıcılar artık kursları doğrudan yayınlayabilir!</p>';
        echo '<p>Kurslar artık "pending" (bekliyor) durumunda değil, doğrudan "publish" (yayınlandı) durumunda oluşturulacak.</p>';
    } else {
        echo '<p style="color: red; font-size: 16px; font-weight: bold;">✗ SORUN VAR: Ayarlar henüz düzgün yapılmamış.</p>';
        echo '<p>Role Custom eklentisinin aktif olduğundan ve doğru çalıştığından emin olun.</p>';
    }
    
    echo '</div>';
}

// WordPress admin panelinde test fonksiyonunu çalıştır
if (is_admin() && current_user_can('manage_options')) {
    add_action('admin_notices', 'test_tutor_course_publishing_settings');
}
