# Role Custom Eklentisi Değişiklik Günlüğü

## Versiyon 1.2.0 - 2025-01-11

### ✨ Yeni Özellikler

#### 🎛️ WordPress Ayarlar Sayfası Eklendi
- **Ayarlar Menüsü**: WordPress Admin → Ayarlar → Role Custom
- **Eğitim Ayarları Bölümü**: Tutor LMS eğitmen yetkilerini yönetme
- **Checkbox Kontrolü**: "Eğitmenler Kursları Direk Yayınlayabilir ve Düzenleyebilir" seçeneği
- **Gerçek Zamanlı Güncelleme**: Ayar değişikliklerinde otomatik senkronizasyon

#### 📊 Gelişmiş Durum Raporlama
- **Durum Kartları**: Görsel durum göstergeleri
- **Detaylı Kontrol**: Tutor LMS entegrasyonu, rol yetkileri, genel durum
- **Renk<PERSON> Gö<PERSON>geler**: <PERSON><PERSON><PERSON><PERSON> (yeşil), <PERSON><PERSON><PERSON> (turuncu), hat<PERSON> (kırmızı)
- **Açıklayıcı Mesajlar**: Her durum için detaylı açıklamalar

#### 🎨 Özel CSS Tasarımı
- **admin-settings.css**: Ayarlar sayfası için özel stil dosyası
- **Responsive Tasarım**: Mobil uyumlu arayüz
- **Modern UI**: WordPress admin temasına uygun tasarım
- **Durum Kutuları**: Bilgi, uyarı, hata ve başarı kutuları

### 🔧 Teknik İyileştirmeler

#### Yeni Metodlar
- `add_settings_page()`: WordPress ayarlar sayfası ekleme
- `register_settings()`: Ayar alanlarını kaydetme
- `sanitize_settings()`: Ayar verilerini temizleme
- `settings_page_content()`: Ayarlar sayfası içeriği
- `display_current_status()`: Gelişmiş durum gösterimi
- `on_settings_updated()`: Ayar güncellendiğinde tetiklenen callback

#### Güncellenmiş Metodlar
- `manage_instructor_course_publishing()`: Ayarlara göre dinamik yönetim
- `enqueue_admin_scripts()`: Ayarlar sayfası CSS desteği

#### Yeni Hook'lar
```php
// Ayarlar sayfası hook'ları
add_action('admin_init', [$this, 'register_settings']);
add_action('admin_menu', [$this, 'add_settings_page'], 10003);
add_action('update_option_role_custom_settings', [$this, 'on_settings_updated'], 10, 2);
```

### 🎯 Kullanıcı Deneyimi İyileştirmeleri

#### Önceki Durum
- Kurs yayınlama izni sabit olarak etkin
- Ayar değiştirmek için kod düzenleme gerekiyordu
- Durum kontrolü sadece test dosyasında

#### Yeni Durum
- WordPress admin panelinden kolay ayar yönetimi
- Checkbox ile tek tıkla açma/kapama
- Gerçek zamanlı durum raporlama
- Görsel geri bildirim

### 📋 Ayar Detayları

#### Role Custom Ayarları
- **Ayar Adı**: `role_custom_settings`
- **Alan**: `instructors_can_publish_courses` (boolean)
- **Varsayılan**: `1` (etkin)

#### Etkin Durumda (Checkbox İşaretli)
- `tutor_option['instructor_can_publish_course']` = 'on'
- `tutor_instructor` rolüne `publish_tutor_courses` yetkisi eklenir
- Kurslar doğrudan "publish" durumunda oluşturulur

#### Pasif Durumda (Checkbox İşaretsiz)
- `tutor_option['instructor_can_publish_course']` = 'off'
- `tutor_instructor` rolünden `publish_tutor_courses` yetkisi kaldırılır
- Kurslar "pending" durumunda oluşturulur

### 🛡️ Güvenlik ve Uyumluluk

- ✅ Sadece `manage_options` yetkisi olan kullanıcılar ayarlara erişebilir
- ✅ Ayar verileri sanitize edilir
- ✅ WordPress Settings API kullanılır
- ✅ Nonce koruması
- ✅ Geriye dönük uyumluluk

---

## Versiyon 1.1.0 - 2025-01-11

### ✨ Yeni Özellikler

#### 🎯 Tutor LMS Kurs Yayınlama İzni Etkinleştirildi
- **Otomatik Ayar Güncellemesi**: `instructor_can_publish_course` ayarı otomatik olarak "on" yapılır
- **Yetki Ekleme**: `publish_tutor_courses` yetkisi Tutor Instructor rolüne otomatik eklenir
- **Doğrudan Yayınlama**: Artık kurslar "pending" (bekliyor) durumunda değil, doğrudan "publish" (yayınlandı) durumunda oluşturulur
- **Admin Onayı Gereksiz**: Instructor'lar admin onayı beklemeden kursları anında yayınlayabilir

#### 🧪 Test Sistemi Eklendi
- **Test Dosyası**: `test-course-publishing.php` dosyası eklendi
- **Otomatik Kontrol**: Ayarların doğru çalışıp çalışmadığını kontrol eder
- **Admin Bildirimi**: WordPress admin panelinde test sonuçlarını gösterir
- **Detaylı Rapor**: Tutor LMS ayarları, rol yetkileri ve kullanıcı durumunu raporlar

### 🔧 Teknik Değişiklikler

#### Yeni Hook'lar
```php
// Kurs yayınlama izni hook'u
add_action('init', [$this, 'enable_instructor_course_publishing'], 20);
```

#### Yeni Metodlar
- `enable_instructor_course_publishing()`: Tutor LMS ayarlarını günceller ve yetkileri ekler

#### Dosya Yapısı
```
role custom/
├── role-custom.php (v1.1.0)
├── test-course-publishing.php (YENİ)
├── README.md (güncellendi)
└── CHANGELOG.md (YENİ)
```

### 📋 Etkilenen Ayarlar

#### Tutor LMS Ayarları
- `tutor_option['instructor_can_publish_course']` = 'on'

#### Tutor Instructor Rolü Yetkileri
- `publish_tutor_courses` yetkisi eklendi (zaten mevcut yetkiler korundu)

### 🎯 Kullanıcı Deneyimi İyileştirmeleri

#### Önceki Durum
1. Instructor kurs oluşturur
2. Kurs "pending" (bekliyor) durumunda kalır
3. Admin onayı gerekir
4. Admin manuel olarak kursu yayınlar

#### Yeni Durum
1. Instructor kurs oluşturur
2. Kurs otomatik olarak "publish" (yayınlandı) durumunda oluşturulur
3. Admin onayı gerekmez
4. Kurs anında yayında

### 🔍 Test Etme

#### Admin Kullanıcılar İçin
1. WordPress admin paneline giriş yapın
2. Admin bildirimleri bölümünde "Tutor LMS Kurs Yayınlama Ayarları Test Sonuçları" kartını kontrol edin
3. Tüm ayarların ✓ işaretli olduğundan emin olun

#### Instructor Kullanıcılar İçin
1. Tutor LMS'de yeni bir kurs oluşturun
2. Kursu kaydettiğinizde "publish" durumunda olduğunu kontrol edin
3. Kursun anında yayında olduğunu doğrulayın

### 🛡️ Güvenlik ve Uyumluluk

- ✅ Admin kullanıcıları etkilenmez
- ✅ Diğer roller etkilenmez
- ✅ Sadece `tutor_instructor` rolü için geçerli
- ✅ Tutor LMS eklentisi aktif değilse çalışmaz
- ✅ Mevcut yetkiler korunur
- ✅ WordPress standartlarına uygun

### 📝 Notlar

- Bu özellik sadece yeni oluşturulan kurslar için geçerlidir
- Mevcut "pending" durumundaki kurslar manuel olarak yayınlanmalıdır
- Test dosyası sadece admin kullanıcılar tarafından görülebilir
- Ayarlar eklenti her etkinleştirildiğinde güncellenir

---

## Versiyon 1.0.0 - 2025-01-10

### 🎉 İlk Sürüm
- Tutor LMS menü kısıtlamaları
- WooCommerce tam erişim
- Role Custom admin menüsü
- Instructor yönetim sistemi
- Otomatik dashboard yönlendirmesi
