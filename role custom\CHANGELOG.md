# Role Custom Eklentisi Değişiklik Günlüğü

## Versiyon 1.1.0 - 2025-01-11

### ✨ Yeni Özellikler

#### 🎯 Tutor LMS Kurs Yayınlama İzni Etkinleştirildi
- **Otomatik Ayar Güncellemesi**: `instructor_can_publish_course` ayarı otomatik olarak "on" yapılır
- **Yet<PERSON>**: `publish_tutor_courses` yetkisi Tutor Instructor rolüne otomatik eklenir
- **Do<PERSON><PERSON>an <PERSON>**: Artık kurslar "pending" (bekliyor) durumunda değil, doğrudan "publish" (yayınlandı) durumunda oluşturulur
- **Admin <PERSON>**: Instructor'lar admin onayı beklemeden kursları anında yayınlayabilir

#### 🧪 Test Sistemi Eklendi
- **Test Dosyası**: `test-course-publishing.php` dos<PERSON>ı eklendi
- **Otomatik Kontrol**: <PERSON><PERSON>lar<PERSON><PERSON> doğru çalışıp çalışmadığını kontrol eder
- **Admin Bildirimi**: WordPress admin panelinde test sonuçlarını gösterir
- **Detaylı Rapor**: Tutor LMS ayarları, rol yetkileri ve kullanıcı durumunu raporlar

### 🔧 Teknik Değişiklikler

#### Yeni Hook'lar
```php
// Kurs yayınlama izni hook'u
add_action('init', [$this, 'enable_instructor_course_publishing'], 20);
```

#### Yeni Metodlar
- `enable_instructor_course_publishing()`: Tutor LMS ayarlarını günceller ve yetkileri ekler

#### Dosya Yapısı
```
role custom/
├── role-custom.php (v1.1.0)
├── test-course-publishing.php (YENİ)
├── README.md (güncellendi)
└── CHANGELOG.md (YENİ)
```

### 📋 Etkilenen Ayarlar

#### Tutor LMS Ayarları
- `tutor_option['instructor_can_publish_course']` = 'on'

#### Tutor Instructor Rolü Yetkileri
- `publish_tutor_courses` yetkisi eklendi (zaten mevcut yetkiler korundu)

### 🎯 Kullanıcı Deneyimi İyileştirmeleri

#### Önceki Durum
1. Instructor kurs oluşturur
2. Kurs "pending" (bekliyor) durumunda kalır
3. Admin onayı gerekir
4. Admin manuel olarak kursu yayınlar

#### Yeni Durum
1. Instructor kurs oluşturur
2. Kurs otomatik olarak "publish" (yayınlandı) durumunda oluşturulur
3. Admin onayı gerekmez
4. Kurs anında yayında

### 🔍 Test Etme

#### Admin Kullanıcılar İçin
1. WordPress admin paneline giriş yapın
2. Admin bildirimleri bölümünde "Tutor LMS Kurs Yayınlama Ayarları Test Sonuçları" kartını kontrol edin
3. Tüm ayarların ✓ işaretli olduğundan emin olun

#### Instructor Kullanıcılar İçin
1. Tutor LMS'de yeni bir kurs oluşturun
2. Kursu kaydettiğinizde "publish" durumunda olduğunu kontrol edin
3. Kursun anında yayında olduğunu doğrulayın

### 🛡️ Güvenlik ve Uyumluluk

- ✅ Admin kullanıcıları etkilenmez
- ✅ Diğer roller etkilenmez
- ✅ Sadece `tutor_instructor` rolü için geçerli
- ✅ Tutor LMS eklentisi aktif değilse çalışmaz
- ✅ Mevcut yetkiler korunur
- ✅ WordPress standartlarına uygun

### 📝 Notlar

- Bu özellik sadece yeni oluşturulan kurslar için geçerlidir
- Mevcut "pending" durumundaki kurslar manuel olarak yayınlanmalıdır
- Test dosyası sadece admin kullanıcılar tarafından görülebilir
- Ayarlar eklenti her etkinleştirildiğinde güncellenir

---

## Versiyon 1.0.0 - 2025-01-10

### 🎉 İlk Sürüm
- Tutor LMS menü kısıtlamaları
- WooCommerce tam erişim
- Role Custom admin menüsü
- Instructor yönetim sistemi
- Otomatik dashboard yönlendirmesi
